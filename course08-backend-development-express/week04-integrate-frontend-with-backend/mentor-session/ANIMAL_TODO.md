# Animal Feature TODO List

This document outlines the tasks required to implement the animal purchase feature.

## Backend

- [x] **Model:** Create an `animal.model.js` in `app/models` with fields for `name`, `species`, `age`, `price`, and `imageUrl`.
- [x] **Repository:** Create an `animal.repository.js` in a new `app/repositories` directory to handle database interactions for the `Animal` model.
- [x] **Service:** Create an `animal.service.js` in `app/services` to contain the business logic for animal-related operations.
- [x] **Controller:** Create an `animal.controller.js` in a new `app/controllers` directory to handle HTTP requests and responses.
- [x] **Routes:** Create an `animal.routes.js` in `app/routes` to define API endpoints for animals.
- [x] **Database:** Update the database schema to include an `animals` table and update the script to populate the database.
- [x] **Server:** Update `server.js` to include the new animal routes.

## Frontend

- [x] **API Service:** Create `animalService.js` in `src/services` to handle API calls to the backend.
- [x] **Animal List Component:** Create `AnimalList.js` in `src/components` to display a list of animals.
- [x] **Animal Details Component:** Create `AnimalDetails.js` in `src/components` to display details for a single animal.
- [x] **Routing:** Add a new route for the animal list page in the main routing file.
- [x] **UI/Styling:** Create or modify CSS files to style the new animal components.
