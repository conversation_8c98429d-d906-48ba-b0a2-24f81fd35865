const mongoose = require("mongoose");
const fs = require("fs");

// Connect to MongoDB
mongoose.connect("mongodb+srv://GreatLearningCourse:<EMAIL>/IntegrateFEwBE--2025-09-20", {
    useNewUrlParser: true,
    useUnifiedTopology: true,
  })
  .then(() => {
    console.log('Connected to MongoDB');
    // Call function to insert all data once connected
    insertAllData();
  })
  .catch((error) => {
    console.error('Error connecting to MongoDB:', error);
  });

// Async Function to Insert multiple
async function insertData(schemaModel, jsonFile) {
  try {
    // Load JSON data from the file
    const jsonData = JSON.parse(fs.readFileSync(jsonFile));

    // Insert data into the collection using insertMany
    await schemaModel.insertMany(jsonData);
    console.log(`Data from "${jsonFile}" inserted successfully!`);
  } catch (err) {
    console.error(`Error inserting data from "${jsonFile}":`, err);
  }
}

const animalSchema = mongoose.Schema(
    {
        name: {
            type: String,
            required: true,
        },
        species: {
            type: String,
            required: true,
        },
        age: {
            type: Number,
            required: true,
        },
        price: {
            type: Number,
            required: true,
        },
        imageUrl: {
            type: String,
            required: false,
        },
        createdTs: {
            type: Date,
            default: new Date(),
        },
        updatedTs: {
            type: Date,
            default: new Date(),
        }
    }
);

const AnimalModel = mongoose.model('animal', animalSchema);


async function insertAllData() {
  try {
    await insertData(AnimalModel, "data/animals.json");
  } catch (error) {
    console.error('Error inserting data:', error);
  } finally {
    // Close the MongoDB connection
    mongoose.connection.close();
    console.log('Connection closed');
  }
}
