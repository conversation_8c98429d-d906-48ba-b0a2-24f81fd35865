const animalRepository = require('../repositories/animal.repository');
const expressAsyncHandler = require("express-async-handler");

const getAnimals = expressAsyncHandler(async (req, res) => {
  try {
    const result = await animalRepository.find();
    res.status(200).json({
      data: result,
      message: "Successfully fetched all animals",
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: "Error fetching all animals details.",
      error: err.message,
    });
  }
});

module.exports = { getAnimals };
