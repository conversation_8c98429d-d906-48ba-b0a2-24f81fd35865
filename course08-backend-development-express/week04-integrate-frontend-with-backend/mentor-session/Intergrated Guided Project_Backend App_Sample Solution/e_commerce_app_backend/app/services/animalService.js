const animalRepository = require('../repositories/animal.repository');
const expressAsyncHandler = require("express-async-handler");

const getAnimals = expressAsyncHandler(async (req, res) => {
  try {
    const result = await animalRepository.find();
    res.status(200).json({
      data: result,
      message: "Successfully fetched all animals",
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: "Error fetching all animals details.",
      error: err.message,
    });
  }
});

const getAnimal = expressAsyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    const result = await animalRepository.findById(id);
    if (!result) {
      return res.status(404).json({
        message: "Animal not found",
      });
    }
    res.status(200).json({
      data: result,
      message: "Successfully fetched animal details",
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: "Error fetching animal details.",
      error: err.message,
    });
  }
});

const createAnimal = expressAsyncHandler(async (req, res) => {
  try {
    const { name, species, age, price, imageUrl } = req.body;

    if (!name || !species || !age || !price) {
      return res.status(400).json({
        message: "Name, species, age, and price are required fields",
      });
    }

    const animalData = {
      name,
      species,
      age: parseInt(age),
      price: parseFloat(price),
      imageUrl: imageUrl || "",
      createdTs: new Date(),
      updatedTs: new Date(),
    };

    const result = await animalRepository.create(animalData);
    res.status(201).json({
      data: result,
      message: "Animal created successfully",
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: "Error creating animal.",
      error: err.message,
    });
  }
});

const updateAnimal = expressAsyncHandler(async (req, res) => {
  try {
    const { id } = req.params;
    const { name, species, age, price, imageUrl } = req.body;

    const existingAnimal = await animalRepository.findById(id);
    if (!existingAnimal) {
      return res.status(404).json({
        message: "Animal not found",
      });
    }

    const updateData = {
      name: name || existingAnimal.name,
      species: species || existingAnimal.species,
      age: age ? parseInt(age) : existingAnimal.age,
      price: price ? parseFloat(price) : existingAnimal.price,
      imageUrl: imageUrl !== undefined ? imageUrl : existingAnimal.imageUrl,
      updatedTs: new Date(),
    };

    const result = await animalRepository.update(id, updateData);
    res.status(200).json({
      data: result,
      message: "Animal updated successfully",
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: "Error updating animal.",
      error: err.message,
    });
  }
});

const deleteAnimal = expressAsyncHandler(async (req, res) => {
  try {
    const { id } = req.params;

    const existingAnimal = await animalRepository.findById(id);
    if (!existingAnimal) {
      return res.status(404).json({
        message: "Animal not found",
      });
    }

    await animalRepository.delete(id);
    res.status(200).json({
      message: "Animal deleted successfully",
    });
  } catch (err) {
    console.error(err);
    res.status(500).json({
      message: "Error deleting animal.",
      error: err.message,
    });
  }
});

module.exports = {
  getAnimals,
  getAnimal,
  createAnimal,
  updateAnimal,
  deleteAnimal
};
