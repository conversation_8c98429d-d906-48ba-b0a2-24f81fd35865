const AnimalModel = require('../models/animalModel');

class AnimalRepository {
    async find() {
        return await AnimalModel.find();
    }

    async findById(id) {
        return await AnimalModel.findById(id);
    }

    async create(animalData) {
        return await AnimalModel.create(animalData);
    }

    async update(id, animalData) {
        return await AnimalModel.findByIdAndUpdate(id, animalData, { new: true });
    }

    async delete(id) {
        return await AnimalModel.findByIdAndDelete(id);
    }
}

module.exports = new AnimalRepository();
