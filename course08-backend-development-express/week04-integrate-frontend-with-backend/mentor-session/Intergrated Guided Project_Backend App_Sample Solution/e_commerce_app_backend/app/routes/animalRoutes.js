const express = require("express");
const { auth, isAdmin } = require("../middleware/authenticationHandler");
const animalRouter = express.Router();

const {
  getAnimals,
  getAnimal,
  createAnimal,
  updateAnimal,
  deleteAnimal
} = require("../services/animalService");

animalRouter.route("/").get(getAnimals);
animalRouter.route("/").post(auth, isAdmin, createAnimal);
animalRouter.route("/:id").get(getAnimal);
animalRouter.route("/:id").put(auth, isAdmin, updateAnimal);
animalRouter.route("/:id").delete(auth, isAdmin, deleteAnimal);

module.exports = animalRouter;
