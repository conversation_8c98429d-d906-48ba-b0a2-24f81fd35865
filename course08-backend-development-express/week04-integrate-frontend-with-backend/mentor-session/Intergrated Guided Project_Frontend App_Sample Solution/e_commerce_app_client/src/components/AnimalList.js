import React, { useState, useEffect } from 'react';
import { getAnimals } from '../services/animalService';

const AnimalList = () => {
  const [animals, setAnimals] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchAnimals = async () => {
      try {
        const response = await getAnimals();
        setAnimals(response.data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAnimals();
  }, []);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error}</div>;
  }

  return (
    <div className="animal-list">
      <h2>Our Animals</h2>
      <div style={{ display: 'flex', flexWrap: 'wrap' }}>
        {animals.map((animal) => (
          <div key={animal._id} style={{ border: '1px solid #ccc', margin: '10px', padding: '10px', width: '200px' }}>
            <img src={animal.imageUrl} alt={animal.name} style={{ width: '100%', height: '150px', objectFit: 'cover' }} />
            <h3>{animal.name}</h3>
            <p>{animal.species}</p>
            <p>Age: {animal.age}</p>
            <p>Price: ${animal.price}</p>
          </div>
        ))}
      </div>
    </div>
  );
};

export default AnimalList;
