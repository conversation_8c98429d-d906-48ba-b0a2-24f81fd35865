import axios from "axios";
import { BACKE<PERSON>_URL_ENDPOINT } from "../constants/backend";

import {
  ANIMAL_CREATE_REQUEST,
  ANIMAL_CREATE_SUCCESS,
  ANIMAL_CREATE_FAILURE,
  ANIMAL_LIST_REQUEST,
  ANIMAL_LIST_SUCCESS,
  ANIMAL_LIST_FAILURE,
  ANIMAL_UPDATE_REQUEST,
  ANIMAL_UPDATE_SUCCESS,
  ANIMAL_UPDATE_FAILURE,
  ANIMAL_DELETE_REQUEST,
  ANIMAL_DELETE_SUCCESS,
  ANIMAL_DELETE_FAILURE,
  ANIMAL_DETAILS_REQUEST,
  ANIMAL_DETAILS_SUCCESS,
  ANIMAL_DETAILS_FAILURE,
} from "../constants/animalActionConstants";

export const addAnimal =
  (name, species, age, price, imageUrl) =>
  async (dispatch) => {
    try {
      dispatch({
        type: ANIMAL_CREATE_REQUEST,
      });

      const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userInfo.sessionToken}`,
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET,PUT,POST,DELETE,PATCH",
        },
      };

      await axios
        .post(BACKEND_URL_ENDPOINT + "animals", { name, species, age, price, imageUrl }, config)
        .then((res) => {
          if (res.status === 201) {
            dispatch({ type: ANIMAL_CREATE_SUCCESS, payload: res.data.message });
          } else {
            dispatch({
              type: ANIMAL_CREATE_FAILURE,
              payload: res.data.message,
            });
          }
        });
    } catch (err) {
      dispatch({
        type: ANIMAL_CREATE_FAILURE,
        payload: err.response?.data?.message || "Error creating animal",
      });
    }
  };

export const deleteAnimal = (id) => async (dispatch) => {
  try {
    dispatch({
      type: ANIMAL_DELETE_REQUEST,
    });

    const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));

    const config = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${userInfo.sessionToken}`,
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET,PUT,POST,DELETE,PATCH",
      },
    };

    await axios
      .delete(BACKEND_URL_ENDPOINT + "animals/" + id, config)
      .then((res) => {
        if (res.status === 200) {
          dispatch({ type: ANIMAL_DELETE_SUCCESS });
        } else {
          dispatch({
            type: ANIMAL_DELETE_FAILURE,
            payload: res.data.message,
          });
        }
      });
  } catch (err) {
    dispatch({
      type: ANIMAL_DELETE_FAILURE,
      payload: err.response?.data?.message || "Error deleting animal",
    });
  }
};

export const fetchAnimalDetails = (id) => async (dispatch) => {
  try {
    dispatch({
      type: ANIMAL_DETAILS_REQUEST,
    });

    const config = {
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET,PUT,POST,DELETE,PATCH",
      },
    };

    await axios
      .get(BACKEND_URL_ENDPOINT + "animals/" + id, config)
      .then((res) => {
        if (res.status === 200) {
          dispatch({
            type: ANIMAL_DETAILS_SUCCESS,
            payload: res.data.data,
          });
        } else {
          dispatch({
            type: ANIMAL_DETAILS_FAILURE,
            payload: res.data.message,
          });
        }
      });
  } catch (err) {
    dispatch({
      type: ANIMAL_DETAILS_FAILURE,
      payload: err.response?.data?.message || "Error fetching animal details",
    });
  }
};

export const updateAnimalDetails =
  (id, name, species, age, price, imageUrl) =>
  async (dispatch) => {
    try {
      dispatch({
        type: ANIMAL_UPDATE_REQUEST,
      });

      const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));

      const config = {
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${userInfo.sessionToken}`,
          "Access-Control-Allow-Origin": "*",
          "Access-Control-Allow-Methods": "GET,PUT,POST,DELETE,PATCH",
        },
      };

      await axios
        .put(BACKEND_URL_ENDPOINT + "animals/" + id, {name, species, age, price, imageUrl}, config)
        .then((res) => {
          if (res.status === 200) {
            dispatch({ type: ANIMAL_UPDATE_SUCCESS, payload: res.data.message });
          } else {
            dispatch({
              type: ANIMAL_UPDATE_FAILURE,
              payload: res.data.message,
            });
          }
        });
    } catch (err) {
      dispatch({
        type: ANIMAL_UPDATE_FAILURE,
        payload: err.response?.data?.message || "Error updating animal",
      });
    }
  };

export const listAnimals = () => async (dispatch) => {
  try {
    dispatch({
      type: ANIMAL_LIST_REQUEST,
    });

    const config = {
      headers: {
        "Content-Type": "application/json",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "GET,PUT,POST,DELETE,PATCH",
      },
    };

    await axios
      .get(BACKEND_URL_ENDPOINT + "animals", config)
      .then((res) => {
        if (res.status === 200) {
          dispatch({
            type: ANIMAL_LIST_SUCCESS,
            payload: res.data.data,
          });
        } else {
          dispatch({
            type: ANIMAL_LIST_FAILURE,
            payload: 'Not able to fetch the animals',
          });
        }
      });
  } catch (err) {
    dispatch({
      type: ANIMAL_LIST_FAILURE,
      payload: 'Not able to fetch the animals',
    });
  }
};
