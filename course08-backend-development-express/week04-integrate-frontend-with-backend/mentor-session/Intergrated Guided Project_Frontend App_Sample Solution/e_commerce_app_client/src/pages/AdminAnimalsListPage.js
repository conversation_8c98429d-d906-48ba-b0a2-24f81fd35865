import React, { useEffect } from "react";
import { Button, Image, Table } from "react-bootstrap";
import { LinkContainer } from "react-router-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { listAnimals, deleteAnimal } from "../actions/animalActions";
import AlertMessage from "../components/AlertMessage";

const AdminAnimalsListPage = () => {
  const dispatch = useDispatch();

  const animalList = useSelector((state) => state.animalList);
  const { loading, success, error, animals } = animalList;
  console.log(loading, success, error, animals);

  const animalDelete = useSelector((state) => state.animalDelete);
  const { successDelete, errorDelete } = animalDelete;

  const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));

  useEffect(() => {
    if (userInfo && userInfo.isAdmin) {
      dispatch(listAnimals());
    } else {
      window.location.replace("/login");
    }
  }, [dispatch, successDelete]);

  const deleteAnimalHandler = (id) => {
    if (window.confirm("Are you sure, you want to delete the animal?")) {
      dispatch(deleteAnimal(id));
    }
  };

  return (
    <>
      {loading && <AlertMessage variant="info" message="Loading..." />}
      {!loading && (
        <LinkContainer to="/admin/animal/new">
          <Button variant="primary" className="my-3">
            Add Animal
          </Button>
        </LinkContainer>
      )}
      {!loading && (!animals || (animals && animals.length === 0)) && (
        <AlertMessage variant="info" message="No animals found" />
      )}
      {success && animals.length !== 0 && (
        <Table striped hover bordered className="table-sm">
          <thead>
            <tr className="text-center">
              <th>Id</th>
              <th>Animal Name</th>
              <th>Animal Image</th>
              <th>Species</th>
              <th>Age</th>
              <th>Price</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {animals.map((animal, index) => (
              <tr key={animal._id} className="text-center">
                <td>{animal._id}</td>
                <td>{animal.name}</td>
                <td>
                  <Image
                    src={animal.imageUrl}
                    width={100}
                    height={100}
                    rounded
                  ></Image>
                </td>
                <td>{animal.species}</td>
                <td>{animal.age}</td>
                <td>${animal.price}</td>
                <td>
                  <Button 
                    variant="info" 
                    className="mb-3 me-2" 
                    onClick={() => window.location.href=`/admin/animal/${animal._id}/edit`}
                  >
                    Edit
                  </Button>
                  <Button
                    variant="danger"
                    className="mb-3"
                    onClick={() => deleteAnimalHandler(animal._id)}
                  >
                    Delete
                  </Button>
                </td>
              </tr>
            ))}
          </tbody>
        </Table>
      )}
      {errorDelete && <AlertMessage variant="danger" message={errorDelete} />}
    </>
  );
};

export default AdminAnimalsListPage;
