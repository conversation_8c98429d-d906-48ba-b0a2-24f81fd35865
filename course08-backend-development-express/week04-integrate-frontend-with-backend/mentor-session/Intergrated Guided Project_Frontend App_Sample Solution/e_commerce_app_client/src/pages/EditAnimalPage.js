import React, { useEffect } from "react";
import { <PERSON><PERSON>, Container, Form, Image } from "react-bootstrap";
import { LinkContainer } from "react-router-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";
import { fetchAnimalDetails, updateAnimalDetails } from "../actions/animalActions";
import AlertMessage from "../components/AlertMessage";

const EditAnimalPage = () => {
  const { id } = useParams();
  console.log(id);

  const [name, setName] = React.useState("");
  const [species, setSpecies] = React.useState("");
  const [age, setAge] = React.useState(0);
  const [price, setPrice] = React.useState(0);
  const [imageUrl, setImageUrl] = React.useState("");

  const handleNameChange = (event) => {
    setName(event.target.value);
  };

  const handleSpeciesChange = (event) => {
    setSpecies(event.target.value);
  };

  const handleAgeChange = (event) => {
    setAge(event.target.value);
  };

  const handlePriceChange = (event) => {
    setPrice(event.target.value);
  };

  const handleImageUrlChange = (event) => {
    setImageUrl(event.target.value);
  };

  const dispatch = useDispatch();
  const { loading, success, error, animal } = useSelector(
    (state) => state.animalDetails
  );

  useEffect(() => {
    dispatch(fetchAnimalDetails(id));
  }, [dispatch]);

  useEffect(() => {
    if (success) {
      setName(animal.name);
      setSpecies(animal.species);
      setAge(animal.age);
      setPrice(animal.price);
      setImageUrl(animal.imageUrl || "");
    }
  }, [success]);

  const dispatchAnimalUpdate = useDispatch();
  const { successUpdate, errorUpdate } = useSelector(
    (state) => state.animalDetailsUpdate
  );

  const editAnimalDetails = (e) => {
    e.preventDefault();
    dispatchAnimalUpdate(
      updateAnimalDetails(
        id,
        name,
        species,
        parseInt(age),
        parseFloat(price),
        imageUrl
      )
    );
  };

  return (
    <>
      <LinkContainer to="/admin/animals">
        <Button variant="primary" className="my-3">
          Show Animals List
        </Button>
      </LinkContainer>
      {loading && (
        <AlertMessage variant="info" message="Loading animal details..." />
      )}
      {error && <AlertMessage variant="danger" message={error} />}
      {successUpdate && (
        <AlertMessage variant="success" message={successUpdate} />
      )}
      {errorUpdate && <AlertMessage variant="danger" message={errorUpdate} />}
      <Container>
        <div className="text-center">
          <Image src={imageUrl} width={200} height={200} rounded></Image>
        </div>
      </Container>
      <Container>
        <h1>Edit Animal</h1>
        <Form onSubmit={editAnimalDetails}>
          <Form.Group className="mb-3" controlId="name">
            <Form.Label>Animal Name</Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter animal name"
              value={name}
              onChange={handleNameChange}
              required
            />
          </Form.Group>

          <Form.Group className="mb-3" controlId="species">
            <Form.Label>Species</Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter species"
              value={species}
              onChange={handleSpeciesChange}
              required
            />
          </Form.Group>

          <Form.Group className="mb-3" controlId="age">
            <Form.Label>Age</Form.Label>
            <Form.Control
              type="number"
              placeholder="Enter age"
              value={age}
              onChange={handleAgeChange}
              required
              min="0"
            />
          </Form.Group>

          <Form.Group className="mb-3" controlId="price">
            <Form.Label>Price</Form.Label>
            <Form.Control
              type="number"
              step="0.01"
              placeholder="Enter price"
              value={price}
              onChange={handlePriceChange}
              required
              min="0"
            />
          </Form.Group>

          <Form.Group className="mb-3" controlId="imageUrl">
            <Form.Label>Image URL</Form.Label>
            <Form.Control
              type="url"
              placeholder="Enter image URL"
              value={imageUrl}
              onChange={handleImageUrlChange}
            />
          </Form.Group>

          <Button variant="primary" type="submit">
            Update Animal
          </Button>
        </Form>
      </Container>
    </>
  );
};

export default EditAnimalPage;
