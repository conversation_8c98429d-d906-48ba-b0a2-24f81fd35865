import React, { useEffect } from "react";
import { <PERSON><PERSON>, Container, Form } from "react-bootstrap";
import { LinkContainer } from "react-router-bootstrap";
import { useDispatch, useSelector } from "react-redux";
import { addAnimal } from "../actions/animalActions";
import AlertMessage from "../components/AlertMessage";

const AddAnimalPage = () => {
  const [name, setName] = React.useState("");
  const [species, setSpecies] = React.useState("");
  const [age, setAge] = React.useState("");
  const [price, setPrice] = React.useState("");
  const [imageUrl, setImageUrl] = React.useState("");

  const handleNameChange = (event) => {
    setName(event.target.value);
  };

  const handleSpeciesChange = (event) => {
    setSpecies(event.target.value);
  };

  const handleAgeChange = (event) => {
    setAge(event.target.value);
  };

  const handlePriceChange = (event) => {
    setPrice(event.target.value);
  };

  const handleImageUrlChange = (event) => {
    setImageUrl(event.target.value);
  };

  const dispatch = useDispatch();
  const animalCreate = useSelector((state) => state.addAnimal);
  const { loading, success, error } = animalCreate;

  const userInfo = JSON.parse(sessionStorage.getItem("userInfo"));

  useEffect(() => {
    if (userInfo && userInfo.isAdmin) {
      // Admin check passed
    } else {
      window.location.replace("/login");
    }
  }, []);

  const createAnimalHandler = (event) => {
    event.preventDefault();
    if (userInfo && userInfo.isAdmin) {
      console.log(name, species, age, price, imageUrl);
      dispatch(addAnimal(name, species, parseInt(age), parseFloat(price), imageUrl));
    } else {
      window.location.replace("/login");
    }
  };

  return (
    <>
      <LinkContainer to="/admin/animals">
        <Button variant="primary" className="my-3">
          Show Animals List
        </Button>
      </LinkContainer>
      {loading && <AlertMessage variant="info" message="Creating animal..." />}
      {success && <AlertMessage variant="success" message={success} />}
      {error && <AlertMessage variant="danger" message={error} />}
      <Container>
        <h1>Add Animal</h1>
        <Form onSubmit={createAnimalHandler}>
          <Form.Group className="mb-3" controlId="name">
            <Form.Label>Animal Name</Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter animal name"
              value={name}
              onChange={handleNameChange}
              required
            />
          </Form.Group>

          <Form.Group className="mb-3" controlId="species">
            <Form.Label>Species</Form.Label>
            <Form.Control
              type="text"
              placeholder="Enter species"
              value={species}
              onChange={handleSpeciesChange}
              required
            />
          </Form.Group>

          <Form.Group className="mb-3" controlId="age">
            <Form.Label>Age</Form.Label>
            <Form.Control
              type="number"
              placeholder="Enter age"
              value={age}
              onChange={handleAgeChange}
              required
              min="0"
            />
          </Form.Group>

          <Form.Group className="mb-3" controlId="price">
            <Form.Label>Price</Form.Label>
            <Form.Control
              type="number"
              step="0.01"
              placeholder="Enter price"
              value={price}
              onChange={handlePriceChange}
              required
              min="0"
            />
          </Form.Group>

          <Form.Group className="mb-3" controlId="imageUrl">
            <Form.Label>Image URL</Form.Label>
            <Form.Control
              type="url"
              placeholder="Enter image URL"
              value={imageUrl}
              onChange={handleImageUrlChange}
            />
          </Form.Group>

          <Button variant="primary" type="submit" disabled={loading}>
            {loading ? "Creating..." : "Create Animal"}
          </Button>
        </Form>
      </Container>
    </>
  );
};

export default AddAnimalPage;
