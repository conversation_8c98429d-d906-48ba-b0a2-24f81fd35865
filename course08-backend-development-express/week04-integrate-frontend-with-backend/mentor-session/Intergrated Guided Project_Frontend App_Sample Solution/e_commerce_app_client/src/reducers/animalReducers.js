import {
  ANIMAL_CREATE_REQUEST,
  ANIMAL_CREATE_SUCCESS,
  ANIMAL_CREATE_FAILURE,
  ANIMAL_LIST_REQUEST,
  ANIMAL_LIST_SUCCESS,
  ANIMAL_LIST_FAILURE,
  ANIMAL_UPDATE_REQUEST,
  ANIMAL_UPDATE_SUCCESS,
  ANIMAL_UPDATE_FAILURE,
  ANIMAL_DELETE_REQUEST,
  ANIMAL_DELETE_SUCCESS,
  ANIMAL_DELETE_FAILURE,
  ANIMAL_DETAILS_REQUEST,
  ANIMAL_DETAILS_SUCCESS,
  ANIMAL_DETAILS_FAILURE,
} from "../constants/animalActionConstants";

export const addAnimalReducer = (state = {}, action) => {
  switch (action.type) {
    case ANIMAL_CREATE_REQUEST:
      return { loading: true };
    case ANIMAL_CREATE_SUCCESS:
      return { loading: false, success: action.payload };
    case ANIMAL_CREATE_FAILURE:
      return { loading: false, error: action.payload };
    default:
      return state;
  }
};

export const deleteAnimalReducer = (state = {}, action) => {
  switch (action.type) {
    case ANIMAL_DELETE_REQUEST:
      return { loading: true };
    case ANIMAL_DELETE_SUCCESS:
      return { loading: false, successDelete: true };
    case ANIMAL_DELETE_FAILURE:
      return { loading: false, errorDelete: action.payload };
    default:
      return state;
  }
};

export const listAnimalsReducer = (state = { animals: [] }, action) => {
  switch (action.type) {
    case ANIMAL_LIST_REQUEST:
      return { loading: true };
    case ANIMAL_LIST_SUCCESS:
      return { loading: false, success: true, animals: action.payload };
    case ANIMAL_LIST_FAILURE:
      return { loading: false, error: action.payload };
    default:
      return state;
  }
};

export const fetchAnimalDetailsReducer = (state = { animal: {} }, action) => {
  switch (action.type) {
    case ANIMAL_DETAILS_REQUEST:
      return { loading: true };
    case ANIMAL_DETAILS_SUCCESS:
      return { loading: false, success: true, animal: action.payload };
    case ANIMAL_DETAILS_FAILURE:
      return { loading: false, error: action.payload };
    default:
      return state;
  }
};

export const updateAnimalDetailsReducer = (
  state = { animal: {} },
  action
) => {
  switch (action.type) {
    case ANIMAL_UPDATE_REQUEST:
      return { loading: true };
    case ANIMAL_UPDATE_SUCCESS:
      return { loading: false, successUpdate: action.payload };
    case ANIMAL_UPDATE_FAILURE:
      return { loading: false, errorUpdate: action.payload };
    default:
      return state;
  }
};
